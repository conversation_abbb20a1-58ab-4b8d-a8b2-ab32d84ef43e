# -*- coding: utf-8 -*-
"""
Script PyRevit simplificado para converter RFA para FBX
Este script funciona dentro do contexto do Revit via PyRevit
"""

# Importações do Revit API (disponíveis no contexto PyRevit)
from Autodesk.Revit.DB import *
from Autodesk.Revit.UI import *
import clr
import System
from System.IO import Path, Directory, File

# Obter documento ativo ou criar novo
doc = __revit__.ActiveUIDocument.Document
app = __revit__.Application

# Caminhos hardcoded para teste
rfa_path = r"C:\Users\<USER>\OneDrive\old\Documentos\bimex-object-market\bimex-object-market\temp\cadeira.rfa"
fbx_path = r"C:\Users\<USER>\OneDrive\old\Documentos\bimex-object-market\bimex-object-market\temp\cadeira.fbx"

print("=== CONVERSÃO RFA PARA FBX ===")
print("RFA: {}".format(rfa_path))
print("FBX: {}".format(fbx_path))

try:
    # Verificar se arquivo RFA existe
    if not File.Exists(rfa_path):
        print("ERRO: Arquivo RFA não encontrado: {}".format(rfa_path))
        exit()

    # Classe para opções de carregamento de família
    class FamilyLoadOptions(IFamilyLoadOptions):
        def OnFamilyFound(self, familyInUse, overwriteParameterValues):
            overwriteParameterValues = True
            return True
        
        def OnSharedFamilyFound(self, sharedFamily, familyInUse, source, overwriteParameterValues):
            source = FamilySource.Family
            overwriteParameterValues = True
            return True

    # Se não há documento ativo, criar um novo
    if not doc or doc.IsFamilyDocument:
        print("INFO: Criando novo documento de projeto...")
        doc = app.NewProjectDocument(UnitSystem.Metric)
        if not doc:
            print("ERRO: Não foi possível criar novo documento")
            exit()

    # Carregar família
    print("INFO: Carregando família...")
    with Transaction(doc, "Carregar Familia") as t:
        t.Start()
        family_load_options = FamilyLoadOptions()
        success = doc.LoadFamily(rfa_path, family_load_options)
        if not success:
            print("ERRO: Falha ao carregar família")
            t.RollBack()
            exit()
        t.Commit()

    print("INFO: Família carregada com sucesso")

    # Encontrar símbolo de família
    family_name = Path.GetFileNameWithoutExtension(rfa_path)
    family_symbol = None
    
    # Procurar em diferentes categorias
    categories = [
        BuiltInCategory.OST_GenericModel,
        BuiltInCategory.OST_Furniture,
        BuiltInCategory.OST_FurnitureSystems,
        BuiltInCategory.OST_Casework
    ]
    
    for category in categories:
        try:
            collector = FilteredElementCollector(doc).OfCategory(category).WhereElementIsElementType()
            for elem in collector:
                if hasattr(elem, 'FamilyName') and family_name.lower() in elem.FamilyName.lower():
                    family_symbol = elem
                    break
            if family_symbol:
                break
        except:
            continue

    if not family_symbol:
        print("ERRO: Símbolo de família não encontrado")
        exit()

    print("INFO: Símbolo encontrado: {}".format(family_symbol.Name))

    # Ativar símbolo se necessário
    if not family_symbol.IsActive:
        with Transaction(doc, "Ativar Simbolo") as t:
            t.Start()
            family_symbol.Activate()
            t.Commit()

    # Instanciar família na origem
    print("INFO: Instanciando família...")
    with Transaction(doc, "Instanciar Familia") as t:
        t.Start()
        
        origin = XYZ(0, 0, 0)
        
        # Tentar encontrar nível
        level_collector = FilteredElementCollector(doc).OfClass(Level).WhereElementIsNotElementType()
        level = level_collector.FirstElement()
        
        instance = None
        if level:
            try:
                instance = doc.Create.NewFamilyInstance(origin, family_symbol, level, Structure.StructuralType.NonStructural)
            except:
                instance = doc.Create.NewFamilyInstance(origin, family_symbol, Structure.StructuralType.NonStructural)
        else:
            instance = doc.Create.NewFamilyInstance(origin, family_symbol, Structure.StructuralType.NonStructural)
        
        if not instance:
            print("ERRO: Falha ao instanciar família")
            t.RollBack()
            exit()
            
        t.Commit()

    print("INFO: Família instanciada com sucesso")

    # Exportar para FBX
    print("INFO: Exportando para FBX...")
    
    # Criar diretório se não existir
    fbx_dir = Path.GetDirectoryName(fbx_path)
    if not Directory.Exists(fbx_dir):
        Directory.CreateDirectory(fbx_dir)
    
    # Encontrar vista 3D
    view_3d = None
    collector = FilteredElementCollector(doc).OfClass(View3D)
    for v in collector:
        if not v.IsTemplate:
            if v.Name == "{3D}":
                view_3d = v
                break
    
    if not view_3d and collector.GetElementCount() > 0:
        for v in collector:
            if not v.IsTemplate:
                view_3d = v
                break

    if not view_3d:
        print("ERRO: Vista 3D não encontrada")
        exit()

    print("INFO: Usando vista: {}".format(view_3d.Name))

    # Configurar exportação
    views_set = ViewSet()
    views_set.Insert(view_3d)
    
    fbx_options = FBXExportOptions()
    fbx_options.UseLevelsOfDetail = False  # Máxima qualidade
    
    # Exportar
    fbx_folder = Path.GetDirectoryName(fbx_path)
    fbx_name = Path.GetFileNameWithoutExtension(fbx_path)
    
    export_success = doc.Export(fbx_folder, fbx_name, views_set, fbx_options)
    
    if export_success:
        print("=== SUCESSO: FBX exportado para {} ===".format(fbx_path))
        
        # Verificar se arquivo foi criado
        if File.Exists(fbx_path):
            file_info = System.IO.FileInfo(fbx_path)
            print("INFO: Tamanho do arquivo: {} bytes".format(file_info.Length))
        else:
            print("AVISO: Arquivo FBX não encontrado após exportação")
    else:
        print("ERRO: Falha na exportação FBX")

except Exception as e:
    print("ERRO: {}".format(str(e)))
    import traceback
    print(traceback.format_exc())

print("INFO: Processo finalizado")
