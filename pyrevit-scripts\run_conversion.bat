@echo off
REM Script para executar conversão RFA para FBX usando Revit + PyRevit
REM Usando os caminhos corretos fornecidos pelo usuário

setlocal enabledelayedexpansion

REM Definir caminhos corretos
set "REVIT_PATH=C:\Program Files\Autodesk\Revit 2024\Revit.exe"
set "PYREVIT_PYTHON=C:\Users\<USER>\AppData\Roaming\pyRevit-Master\bin\cengines\CPY3123\python.exe"
set "PYREVIT_PATH=C:\Users\<USER>\AppData\Roaming\pyRevit-Master"
set "RFA_FILE=C:\Users\<USER>\OneDrive\old\Documentos\bimex-object-market\bimex-object-market\temp\cadeira.rfa"
set "FBX_FILE=C:\Users\<USER>\OneDrive\old\Documentos\bimex-object-market\bimex-object-market\temp\cadeira.fbx"
set "SCRIPT_PATH=%~dp0simple_rfa_to_fbx.py"

echo ========================================
echo CONVERSAO RFA PARA FBX - Revit + PyRevit
echo ========================================
echo Revit: %REVIT_PATH%
echo PyRevit: %PYREVIT_PATH%
echo RFA: %RFA_FILE%
echo FBX: %FBX_FILE%
echo Script: %SCRIPT_PATH%
echo ========================================

REM Verificar se os arquivos existem
if not exist "%REVIT_PATH%" (
    echo ERRO: Revit nao encontrado em: %REVIT_PATH%
    exit /b 1
)

if not exist "%PYREVIT_PATH%" (
    echo ERRO: PyRevit nao encontrado em: %PYREVIT_PATH%
    exit /b 1
)

if not exist "%RFA_FILE%" (
    echo ERRO: Arquivo RFA nao encontrado: %RFA_FILE%
    exit /b 1
)

if not exist "%SCRIPT_PATH%" (
    echo ERRO: Script nao encontrado: %SCRIPT_PATH%
    exit /b 1
)

echo INFO: Iniciando Revit com PyRevit...

REM Mudar para o diretório do PyRevit
cd /d "%PYREVIT_PATH%"

REM Tentar executar usando pyrevit run com um modelo vazio
echo INFO: Tentando executar com pyrevit run...

REM Criar um arquivo de journal temporário para automatizar o Revit
set "JOURNAL_FILE=%TEMP%\rfa_to_fbx_journal.txt"

echo 'Creating journal file: %JOURNAL_FILE%
echo Jrn.Command "Ribbon" , "Create a new project , ID_REVIT_FILE_NEW_PROJECT" > "%JOURNAL_FILE%"
echo Jrn.Data "TaskDialogResult" , "This action will close the active project. Do you want to save the changes to Untitled?" , "No", "IDNO" >> "%JOURNAL_FILE%"
echo Jrn.Command "Ribbon" , "Run a pyRevit command , ID_PYREVIT_COMMAND" >> "%JOURNAL_FILE%"
echo Jrn.Data "PyRevitCommand" , "%SCRIPT_PATH%" >> "%JOURNAL_FILE%"
echo Jrn.Command "SystemMenu" , "Quit the application; prompts to save projects , ID_APP_EXIT" >> "%JOURNAL_FILE%"

echo INFO: Executando Revit com journal...
"%REVIT_PATH%" "%JOURNAL_FILE%"

REM Verificar se o arquivo FBX foi criado
if exist "%FBX_FILE%" (
    echo ========================================
    echo SUCESSO: Arquivo FBX criado!
    echo Local: %FBX_FILE%
    for %%F in ("%FBX_FILE%") do echo Tamanho: %%~zF bytes
    echo ========================================
    exit /b 0
) else (
    echo ========================================
    echo ERRO: Arquivo FBX nao foi criado
    echo ========================================
    exit /b 1
)
