# -*- coding: utf-8 -*-
"""
Script direto para conversão RFA para FBX
Este script deve ser executado dentro do Revit com PyRevit carregado
"""

import sys
import os

# Adicionar o caminho do script ao sys.path para importações
script_dir = os.path.dirname(__file__)
if script_dir not in sys.path:
    sys.path.append(script_dir)

def run_conversion():
    """Executa a conversão RFA para FBX"""
    try:
        # Importar o módulo principal
        import export_rfa_to_fbx
        
        # Caminhos para teste
        rfa_path = r"C:\Users\<USER>\OneDrive\old\Documentos\bimex-object-market\bimex-object-market\temp\cadeira.rfa"
        fbx_path = r"C:\Users\<USER>\OneDrive\old\Documentos\bimex-object-market\bimex-object-market\temp\cadeira.fbx"
        
        print("=== INICIANDO CONVERSÃO DIRETA ===")
        print("RFA: {}".format(rfa_path))
        print("FBX: {}".format(fbx_path))
        
        # Executar a conversão
        success = export_rfa_to_fbx.export_rfa_to_fbx_headless(rfa_path, fbx_path)
        
        if success:
            print("=== CONVERSÃO CONCLUÍDA COM SUCESSO ===")
            return True
        else:
            print("=== CONVERSÃO FALHOU ===")
            return False
            
    except Exception as e:
        print("ERRO na conversão direta: {}".format(str(e)))
        import traceback
        print(traceback.format_exc())
        return False

# Executar se chamado diretamente
if __name__ == "__main__":
    success = run_conversion()
    if success:
        sys.exit(0)
    else:
        sys.exit(1)
