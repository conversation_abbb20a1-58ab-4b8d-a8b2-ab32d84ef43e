# coding: utf-8
"""
Script PyRevit para carregar uma família RFA em um novo projeto,
instanciá-la na origem e exportar para FBX.

Uso via CLI pyRevit:
pyrevit run export_rfa_to_fbx.py -- <caminho_para_rfa> <caminho_para_fbx_saida>

Exemplo:
pyrevit run export_rfa_to_fbx.py -- "C:\Users\<USER>\Downloads\Furniture_Chairs_Plank_Blocco-Chair.rfa" "C:\temp\output.fbx"
"""

import clr
import sys
import os

# Adicionar referências da API do Revit
clr.AddReference('RevitAPI')
clr.AddReference('RevitAPIUI')

from Autodesk.Revit.DB import (
    Transaction,
    FilteredElementCollector,
    BuiltInCategory,
    IFamilyLoadOptions,
    FamilySource,
    ViewSet,
    FBXExportOptions,
    XYZ,
    ElementId,
    View3D,
    Level,
    UnitSystem,
    Structure,
    FamilyInstance
)

# Para manipulação de caminhos
from System.IO import Path, Directory, File

class FamilyLoadOptionsOverride(IFamilyLoadOptions):
    """Classe para controlar opções de carregamento de famílias"""

    def OnFamilyFound(self, familyInUse, overwriteParameterValues):
        """Callback quando uma família já existe no projeto"""
        overwriteParameterValues = True  # Sobrescrever parâmetros se a família já existir
        return True

    def OnSharedFamilyFound(self, sharedFamily, familyInUse, source, overwriteParameterValues):
        """Callback quando uma família compartilhada é encontrada"""
        source = FamilySource.Family  # Priorizar a família do arquivo
        overwriteParameterValues = True
        return True

def create_new_project_document(app):
    """Cria um novo documento de projeto Revit"""
    try:
        print("INFO: Criando um novo documento de projeto...")

        # Tentar criar um projeto usando sistema métrico
        doc = app.NewProjectDocument(UnitSystem.Metric)

        if not doc:
            print("ERRO: Não foi possível criar um novo documento de projeto.")
            return None

        print("INFO: Novo documento de projeto criado com sucesso.")
        return doc

    except Exception as e:
        print("ERRO: Falha ao criar novo documento: {0}".format(str(e)))
        return None

def load_family_into_project(doc, rfa_file_path):
    """Carrega uma família RFA no projeto"""
    try:
        print("INFO: Carregando família de: {0}".format(rfa_file_path))

        # Verificar se o arquivo existe
        if not File.Exists(rfa_file_path):
            print("ERRO: Arquivo RFA não encontrado: {0}".format(rfa_file_path))
            return None

        # Criar opções de carregamento
        family_load_options = FamilyLoadOptionsOverride()

        # Carregar a família
        with Transaction(doc, "Carregar Familia RFA") as t:
            t.Start()

            # Carregar família usando a API correta
            success = doc.LoadFamily(rfa_file_path, family_load_options)

            if not success:
                print("ERRO: Falha ao carregar família de {0}".format(rfa_file_path))
                t.RollBack()
                return None

            t.Commit()

        # Encontrar a família carregada
        collector = FilteredElementCollector(doc).OfClass(clr.GetClrType(type(doc.GetElement(ElementId(1)))))
        families = [elem for elem in collector if hasattr(elem, 'Name') and elem.Name in rfa_file_path]

        if not families:
            # Tentar uma abordagem diferente para encontrar a família
            all_families = FilteredElementCollector(doc).OfCategory(BuiltInCategory.OST_GenericModel).WhereElementIsElementType()
            for family in all_families:
                if hasattr(family, 'FamilyName'):
                    family_name = Path.GetFileNameWithoutExtension(rfa_file_path)
                    if family_name.lower() in family.FamilyName.lower():
                        print("INFO: Família '{0}' carregada com sucesso.".format(family.FamilyName))
                        return family

        print("INFO: Família carregada com sucesso.")
        return True

    except Exception as e:
        print("ERRO: Exceção ao carregar família: {0}".format(str(e)))
        return None

def find_and_activate_family_symbol(doc, rfa_file_path):
    """Encontra e ativa um símbolo de família"""
    try:
        print("INFO: Procurando símbolos de família...")

        # Obter nome da família do arquivo
        family_name = Path.GetFileNameWithoutExtension(rfa_file_path)

        # Procurar por símbolos de família
        collector = FilteredElementCollector(doc).OfCategory(BuiltInCategory.OST_GenericModel).WhereElementIsElementType()

        family_symbol = None
        for elem in collector:
            if hasattr(elem, 'FamilyName') and family_name.lower() in elem.FamilyName.lower():
                family_symbol = elem
                break

        # Se não encontrou, tentar outras categorias
        if not family_symbol:
            categories = [
                BuiltInCategory.OST_Furniture,
                BuiltInCategory.OST_FurnitureSystems,
                BuiltInCategory.OST_Casework,
                BuiltInCategory.OST_SpecialityEquipment,
                BuiltInCategory.OST_PlumbingFixtures,
                BuiltInCategory.OST_ElectricalFixtures,
                BuiltInCategory.OST_LightingFixtures
            ]

            for category in categories:
                try:
                    collector = FilteredElementCollector(doc).OfCategory(category).WhereElementIsElementType()
                    for elem in collector:
                        if hasattr(elem, 'FamilyName') and family_name.lower() in elem.FamilyName.lower():
                            family_symbol = elem
                            break
                    if family_symbol:
                        break
                except:
                    continue

        if not family_symbol:
            print("ERRO: Nenhum símbolo de família encontrado para {0}".format(family_name))
            return None

        # Ativar o símbolo se necessário
        if not family_symbol.IsActive:
            with Transaction(doc, "Ativar Simbolo de Familia") as t_activate:
                t_activate.Start()
                family_symbol.Activate()
                t_activate.Commit()

        print("INFO: Usando o símbolo de família: {0}".format(family_symbol.Name))
        return family_symbol

    except Exception as e:
        print("ERRO: Exceção ao encontrar símbolo de família: {0}".format(str(e)))
        return None

def create_family_instance(doc, family_symbol):
    """Cria uma instância da família na origem"""
    try:
        print("INFO: Instanciando a família na origem...")

        with Transaction(doc, "Instanciar Familia") as t_place:
            t_place.Start()

            origin = XYZ(0, 0, 0)

            # Tentar encontrar um nível padrão
            level_collector = FilteredElementCollector(doc).OfClass(Level).WhereElementIsNotElementType()
            level = level_collector.FirstElement()

            instance = None

            if not level:
                print("AVISO: Nenhum nível padrão encontrado. Tentando instanciar sem host específico.")
                try:
                    # Tentar instanciar sem nível
                    instance = doc.Create.NewFamilyInstance(origin, family_symbol, Structure.StructuralType.NonStructural)
                except:
                    print("ERRO: Não foi possível instanciar sem nível.")
                    t_place.RollBack()
                    return None
            else:
                print("INFO: Instanciando no nível: {0}".format(level.Name))
                try:
                    instance = doc.Create.NewFamilyInstance(origin, family_symbol, level, Structure.StructuralType.NonStructural)
                except:
                    # Se falhar com nível, tentar sem nível
                    print("AVISO: Falha ao instanciar com nível, tentando sem nível...")
                    try:
                        instance = doc.Create.NewFamilyInstance(origin, family_symbol, Structure.StructuralType.NonStructural)
                    except Exception as e2:
                        print("ERRO: Falha ao instanciar família: {0}".format(str(e2)))
                        t_place.RollBack()
                        return None

            if not instance:
                print("ERRO: Falha ao instanciar a família.")
                t_place.RollBack()
                return None

            t_place.Commit()

        print("INFO: Instância da família criada com sucesso.")
        return instance

    except Exception as e:
        print("ERRO: Exceção ao criar instância: {0}".format(str(e)))
        return None

def export_to_fbx(doc, fbx_output_path):
    """Exporta o modelo para FBX"""
    try:
        print("INFO: Exportando para FBX: {0}".format(fbx_output_path))

        # Separar diretório e nome do arquivo
        fbx_output_folder = Path.GetDirectoryName(fbx_output_path)
        fbx_file_name = Path.GetFileNameWithoutExtension(fbx_output_path)

        # Criar diretório se não existir
        if not Directory.Exists(fbx_output_folder):
            Directory.CreateDirectory(fbx_output_folder)

        # Encontrar vista 3D para exportação
        view_3d_to_export = None
        collector = FilteredElementCollector(doc).OfClass(View3D)

        for v in collector:
            if not v.IsTemplate:
                # Tentar encontrar a vista 3D padrão "{3D}"
                if v.Name == "{3D}":
                    view_3d_to_export = v
                    break

        if not view_3d_to_export and collector.GetElementCount() > 0:
            # Se "{3D}" não for encontrada, pegar a primeira vista 3D não template
            for v_fallback in collector:
                if not v_fallback.IsTemplate:
                    view_3d_to_export = v_fallback
                    break

        if not view_3d_to_export:
            print("ERRO: Nenhuma vista 3D adequada encontrada para exportação FBX.")
            return False

        print("INFO: Usando a vista para exportação: {0}".format(view_3d_to_export.Name))

        # Criar conjunto de vistas para exportação
        views_to_export_set = ViewSet()
        views_to_export_set.Insert(view_3d_to_export)

        # Configurar opções de exportação FBX
        fbx_options = FBXExportOptions()
        # Configurações para máxima qualidade geométrica
        fbx_options.UseLevelsOfDetail = False  # Não usar LOD para manter detalhes

        # Exportar
        export_success = doc.Export(fbx_output_folder, fbx_file_name, views_to_export_set, fbx_options)

        if not export_success:
            print("ERRO: Exportação para FBX falhou.")
            return False

        print("INFO: Exportado com sucesso para {0}".format(fbx_output_path))
        return True

    except Exception as e:
        print("ERRO: Exceção durante exportação FBX: {0}".format(str(e)))
        return False

def export_rfa_to_fbx_headless(rfa_file_path, fbx_output_path):
    """
    Função principal: Carrega uma RFA em um novo projeto e exporta para FBX.
    """
    doc = None
    try:
        print("=== INICIANDO CONVERSÃO RFA PARA FBX ===")
        print("RFA: {0}".format(rfa_file_path))
        print("FBX: {0}".format(fbx_output_path))

        # Obter o objeto Application do Revit a partir do contexto pyRevit
        app = __revit__.Application

        # 1. Criar um novo documento de projeto
        doc = create_new_project_document(app)
        if not doc:
            return False

        # 2. Carregar a família RFA
        load_result = load_family_into_project(doc, rfa_file_path)
        if not load_result:
            return False

        # 3. Encontrar e ativar símbolo de família
        family_symbol = find_and_activate_family_symbol(doc, rfa_file_path)
        if not family_symbol:
            return False

        # 4. Instanciar a família na origem
        instance = create_family_instance(doc, family_symbol)
        if not instance:
            return False

        # 5. Exportar para FBX
        export_success = export_to_fbx(doc, fbx_output_path)
        if not export_success:
            return False

        print("=== CONVERSÃO CONCLUÍDA COM SUCESSO ===")
        return True

    except Exception as e:
        print("ERRO INESPERADO: {0}".format(str(e)))
        import traceback
        print(traceback.format_exc())
        return False

    finally:
        # 6. Fechar o documento sem salvar alterações
        if doc and doc.IsValidObject:
            print("INFO: Fechando o documento do projeto sem salvar...")
            doc.Close(False)
        print("INFO: Processo finalizado.")

if __name__ == "__main__":
    # Argumentos são passados após "--" ao usar "pyrevit run"
    # Ex: pyrevit run script.py -- arg1 arg2
    script_args = []
    try:
        # Encontra o índice de "--" e pega os argumentos subsequentes
        separator_index = sys.argv.index("--")
        script_args = sys.argv[separator_index + 1:]
    except ValueError:
        # "--" não encontrado, talvez o script esteja sendo chamado de outra forma
        script_args = sys.argv[1:] if len(sys.argv) > 1 else []

    if len(script_args) < 2:
        print("ERRO DE USO: Caminho do RFA e caminho de saída do FBX são necessários.")
        print("Exemplo: pyrevit run export_rfa_to_fbx.py -- \"C:\\caminho\\familia.rfa\" \"C:\\caminho\\saida.fbx\"")
        sys.exit(1)

    rfa_input_path = script_args[0].strip('"')
    fbx_output_path_arg = script_args[1].strip('"')

    # Validar caminhos
    if not os.path.exists(rfa_input_path):
        print("ERRO: Arquivo RFA não encontrado: {0}".format(rfa_input_path))
        sys.exit(1)

    # Garantir extensão .fbx
    if not fbx_output_path_arg.lower().endswith('.fbx'):
        fbx_output_path_arg += '.fbx'

    print("Iniciando conversão...")
    print("RFA: {0}".format(rfa_input_path))
    print("FBX: {0}".format(fbx_output_path_arg))

    success = export_rfa_to_fbx_headless(rfa_input_path, fbx_output_path_arg)

    if success:
        print("--- EXPORTAÇÃO RFA PARA FBX CONCLUÍDA COM SUCESSO ---")
        sys.exit(0)
    else:
        print("--- EXPORTAÇÃO RFA PARA FBX FALHOU ---")
        sys.exit(1)