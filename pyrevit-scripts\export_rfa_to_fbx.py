# coding: utf-8
"""
Script pyRevit para carregar uma família RFA em um novo projeto,
instanciá-la na origem e exportar para FBX.

Uso via CLI pyRevit:
pyrevit run export_rfa_to_fbx.py -- <caminho_para_rfa> <caminho_para_fbx_saida>
"""

import clr
import sys
import os

# Adicionar referências da API do Revit
clr.AddReference('RevitAPI')
clr.AddReference('RevitAPIUI')

from Autodesk.Revit.DB import (
    Transaction, 
    FilteredElementCollector, 
    BuiltInCategory, 
    IFamilyLoadOptions,
    FamilySource,
    ViewSet,
    FBXExportOptions,
    XYZ,
    ElementId,
    View3D,
    Level
)
# Importar UnitSystem para NewProjectDocument
from Autodesk.Revit.DB import UnitSystem

# Para manipulação de caminhos
from System.IO import Path, Directory

class FamilyLoadOptionsOverride(IFamilyLoadOptions):
    def OnFamilyFound(self, familyInUse, overwriteParameterValues):
        overwriteParameterValues = True # Sobrescrever parâmetros se a família já existir
        return True

    def OnSharedFamilyFound(self, sharedFamily, familyInUse, source, overwriteParameterValues):
        source = FamilySource.Family # Priorizar a família do arquivo
        overwriteParameterValues = True
        return True

def export_rfa_to_fbx_headless(rfa_file_path, fbx_output_path):
    """
    Carrega uma RFA em um novo projeto e exporta para FBX.
    """
    doc = None  # Inicializar doc para o bloco finally
    try:
        # Obter o objeto Application do Revit a partir do contexto pyRevit
        app = __revit__.Application

        # 1. Criar um novo documento de projeto
        print("INFO: Criando um novo documento de projeto...")
        # Tenta criar um projeto usando um sistema de unidades, o que geralmente não requer um template explícito.
        # Se isso falhar, um caminho para um template mínimo seria necessário.
        doc = app.NewProjectDocument(UnitSystem.Metric) 
        if not doc:
            print("ERRO: Nao foi possivel criar um novo documento de projeto.")
            return False
        print("INFO: Novo documento de projeto criado.")

        # Garantir que o diretório de saída exista
        fbx_output_folder = Path.GetDirectoryName(fbx_output_path)
        fbx_file_name = Path.GetFileName(fbx_output_path)
        if not Directory.Exists(fbx_output_folder):
            Directory.CreateDirectory(fbx_output_folder)

        # 2. Carregar a família RFA
        print(f"INFO: Carregando familia de: {rfa_file_path}")
        loaded_family = None
        family_load_options = FamilyLoadOptionsOverride()
        
        with Transaction(doc, "Carregar Familia RFA") as t:
            t.Start()
            # A variável 'loaded_family' será preenchida pela API se o carregamento for bem-sucedido
            # É necessário passar uma referência que pode ser modificada (out loaded_family em C#)
            # Em PythonNet, isso geralmente é tratado retornando o objeto ou usando uma lista/wrapper.
            # A assinatura correta para LoadFamily que retorna a família é mais simples:
            # success = doc.LoadFamily(rfa_file_path, family_load_options, out loaded_family_ref)
            # No entanto, uma forma mais idiomática em PythonNet é:
            loaded_family = doc.LoadFamily(rfa_file_path, family_load_options)
            
            if not loaded_family:
                print(f"ERRO: Falha ao carregar familia de {rfa_file_path}")
                t.RollBack()
                return False # doc.Close será chamado no finally
            t.Commit()
        print(f"INFO: Familia '{loaded_family.Name}' carregada com sucesso.")

        # 3. Encontrar um FamilySymbol para instanciar
        family_symbol = None
        symbol_ids = loaded_family.GetFamilySymbolIds()
        if not symbol_ids or symbol_ids.Count == 0:
            print(f"ERRO: Nenhum simbolo de familia (FamilySymbol) encontrado em {loaded_family.Name}")
            return False

        # Obter o primeiro símbolo de família disponível
        first_symbol_id = list(symbol_ids)[0]
        family_symbol = doc.GetElement(first_symbol_id)

        # Garantir que o símbolo está ativo
        if family_symbol and not family_symbol.IsActive:
            with Transaction(doc, "Ativar Simbolo de Familia") as t_activate:
                t_activate.Start()
                family_symbol.Activate()
                t_activate.Commit()
        print(f"INFO: Usando o simbolo de familia: {family_symbol.Name}")

        # 4. Instanciar a família na origem
        print("INFO: Instanciando a familia na origem...")
        with Transaction(doc, "Instanciar Familia") as t_place:
            t_place.Start()
            origin = XYZ(0, 0, 0)
            
            # Tentar encontrar um nível padrão para instanciar. Muitas famílias requerem um host.
            level = FilteredElementCollector(doc).OfClass(Level).WhereElementIsNotElementType().FirstElement()
            
            instance = None
            if not level:
                print("AVISO: Nenhum nivel padrao encontrado. Tentando instanciar sem host especifico.")
                # Algumas famílias podem ser instanciadas sem um nível (ex: baseadas em plano de trabalho ou independentes)
                instance = doc.Create.NewFamilyInstance(origin, family_symbol, Structure.StructuralType.NonStructural)
            else:
                print(f"INFO: Instanciando no nivel: {level.Name}")
                instance = doc.Create.NewFamilyInstance(origin, family_symbol, level, Structure.StructuralType.NonStructural)

            if not instance:
                print("ERRO: Falha ao instanciar a familia.")
                t_place.RollBack()
                return False
            t_place.Commit()
        print("INFO: Instancia da familia criada com sucesso.")

        # 5. Exportar para FBX
        print(f"INFO: Exportando para FBX: {fbx_output_path}...")
        view_3d_to_export = None
        collector = FilteredElementCollector(doc).OfClass(View3D)
        for v in collector:
            if not v.IsTemplate:
                # Tentar encontrar a vista 3D padrão "{3D}"
                if v.Name == "{3D}":
                    view_3d_to_export = v
                    break
        
        if not view_3d_to_export and collector.GetElementCount() > 0:
             # Se "{3D}" não for encontrada, pegar a primeira vista 3D não template
            for v_fallback in collector:
                if not v_fallback.IsTemplate:
                    view_3d_to_export = v_fallback
                    break

        if not view_3d_to_export:
            print("ERRO: Nenhuma vista 3D adequada encontrada para exportacao FBX.")
            return False
        print(f"INFO: Usando a vista para exportacao: {view_3d_to_export.Name}")

        views_to_export_set = ViewSet()
        views_to_export_set.Insert(view_3d_to_export)

        fbx_options = FBXExportOptions()
        # Opções podem ser customizadas aqui se necessário, ex: fbx_options.UseLevelsOfDetail = False

        # O método Export não precisa de transação, mas se precisasse, seria aqui.
        export_success = doc.Export(fbx_output_folder, fbx_file_name, views_to_export_set, fbx_options)
        
        if not export_success:
            print("ERRO: Exportacao para FBX falhou.")
            return False

        print(f"INFO: Exportado com sucesso para {fbx_output_path}")
        return True

    except Exception as e:
        print(f"ERRO INESPERADO: {str(e)}")
        import traceback
        print(traceback.format_exc())
        return False
    finally:
        # 6. Fechar o documento sem salvar alterações
        if doc and doc.IsValidObject:
            print("INFO: Fechando o documento do projeto sem salvar...")
            doc.Close(False)
        print("INFO: Processo finalizado.")

if __name__ == "__main__":
    # Argumentos são passados após "--" ao usar "pyrevit run"
    # Ex: pyrevit run script.py -- arg1 arg2
    script_args = []
    try:
        # Encontra o índice de "--" e pega os argumentos subsequentes
        separator_index = sys.argv.index("--")
        script_args = sys.argv[separator_index + 1:]
    except ValueError:
        # "--" não encontrado, talvez o script esteja sendo chamado de outra forma ou sem argumentos
        pass 

    if len(script_args) < 2:
        print("ERRO DE USO: Caminho do RFA e caminho de saida do FBX sao necessarios.")
        print("Exemplo: pyrevit run export_rfa_to_fbx.py -- "C:\caminho\familia.rfa" "C:\caminho\saida.fbx"")
        sys.exit(1)

    rfa_input_path = script_args[0]
    fbx_output_path_arg = script_args[1]

    print(f"--- Iniciando Exportacao RFA para FBX ---")
    print(f"Arquivo RFA de entrada: {rfa_input_path}")
    print(f"Arquivo FBX de saida: {fbx_output_path_arg}")

    if not os.path.exists(rfa_input_path):
        print(f"ERRO: Arquivo RFA nao encontrado em: {rfa_input_path}")
        sys.exit(1)
    
    # Validar extensão do arquivo de saída
    if not fbx_output_path_arg.lower().endswith(".fbx"):
        print(f"AVISO: O caminho de saida '{fbx_output_path_arg}' nao termina com .fbx. Adicionando extensao.")
        fbx_output_path_arg += ".fbx"

    success = export_rfa_to_fbx_headless(rfa_input_path, fbx_output_path_arg)

    if success:
        print("--- Exportacao RFA para FBX concluida com sucesso ---")
        sys.exit(0)
    else:
        print("--- Exportacao RFA para FBX falhou ---")
        sys.exit(1)
