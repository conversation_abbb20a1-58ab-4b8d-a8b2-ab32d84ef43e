#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script de teste para conversão RFA para FBX usando PyRevit
Este script pode ser usado para testar a conversão sem precisar do PyRevit instalado
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def print_colored(message, color="white"):
    """Imprime mensagem colorida no console"""
    colors = {
        "red": "\033[91m",
        "green": "\033[92m",
        "yellow": "\033[93m",
        "blue": "\033[94m",
        "magenta": "\033[95m",
        "cyan": "\033[96m",
        "white": "\033[97m",
        "reset": "\033[0m"
    }
    
    color_code = colors.get(color.lower(), colors["white"])
    reset_code = colors["reset"]
    print(f"{color_code}{message}{reset_code}")

def check_file_exists(file_path, description):
    """Verifica se um arquivo existe"""
    if not os.path.exists(file_path):
        print_colored(f"ERRO: {description} não encontrado em: {file_path}", "red")
        return False
    return True

def run_pyrevit_conversion(rfa_path, fbx_path):
    """Executa a conversão RFA para FBX usando PyRevit"""
    
    # Caminhos padrão
    pyrevit_path = r"C:\Users\<USER>\AppData\Roaming\pyRevit-Master"
    revit_path = r"C:\Program Files\Autodesk\Revit 2024\Revit.exe"
    script_path = os.path.join(os.path.dirname(__file__), "export_rfa_to_fbx.py")
    
    print_colored("=" * 50, "cyan")
    print_colored("CONVERSÃO RFA PARA FBX - PyRevit Headless", "cyan")
    print_colored("=" * 50, "cyan")
    print_colored(f"RFA Input: {rfa_path}", "yellow")
    print_colored(f"FBX Output: {fbx_path}", "yellow")
    print_colored(f"PyRevit Path: {pyrevit_path}", "yellow")
    print_colored("=" * 50, "cyan")
    
    # Verificar se todos os arquivos existem
    checks = [
        (rfa_path, "Arquivo RFA"),
        (pyrevit_path, "PyRevit"),
        (revit_path, "Revit"),
        (script_path, "Script PyRevit")
    ]
    
    for file_path, description in checks:
        if not check_file_exists(file_path, description):
            return False
    
    # Criar diretório de saída se não existir
    fbx_dir = os.path.dirname(fbx_path)
    if not os.path.exists(fbx_dir):
        print_colored(f"INFO: Criando diretório de saída: {fbx_dir}", "green")
        os.makedirs(fbx_dir, exist_ok=True)
    
    print_colored("INFO: Iniciando conversão RFA para FBX...", "green")
    
    try:
        # Mudar para o diretório do PyRevit
        original_dir = os.getcwd()
        os.chdir(pyrevit_path)
        
        # Construir comando PyRevit
        cmd = [
            "pyrevit",
            "run",
            script_path,
            "--",
            rfa_path,
            fbx_path
        ]
        
        print_colored(f"INFO: Executando comando: {' '.join(cmd)}", "blue")
        
        # Executar comando
        start_time = time.time()
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)  # 5 minutos timeout
        end_time = time.time()
        
        # Voltar ao diretório original
        os.chdir(original_dir)
        
        print_colored(f"INFO: Tempo de execução: {end_time - start_time:.2f} segundos", "blue")
        
        if result.returncode == 0:
            print_colored("=" * 50, "green")
            print_colored("SUCESSO: Conversão RFA para FBX concluída!", "green")
            print_colored(f"Arquivo FBX criado em: {fbx_path}", "green")
            print_colored("=" * 50, "green")
            
            # Verificar se o arquivo FBX foi realmente criado
            if os.path.exists(fbx_path):
                file_size = os.path.getsize(fbx_path)
                print_colored(f"INFO: Tamanho do arquivo FBX: {file_size} bytes", "green")
            else:
                print_colored("AVISO: Arquivo FBX não encontrado após conversão", "yellow")
            
            return True
        else:
            print_colored("=" * 50, "red")
            print_colored("ERRO: Falha na conversão RFA para FBX", "red")
            print_colored(f"Código de erro: {result.returncode}", "red")
            print_colored("=" * 50, "red")
            
            if result.stdout:
                print_colored("STDOUT:", "yellow")
                print(result.stdout)
            
            if result.stderr:
                print_colored("STDERR:", "red")
                print(result.stderr)
            
            return False
            
    except subprocess.TimeoutExpired:
        print_colored("ERRO: Timeout na execução do comando (5 minutos)", "red")
        return False
    except Exception as e:
        print_colored(f"ERRO: Exceção durante execução: {str(e)}", "red")
        return False
    finally:
        # Garantir que voltamos ao diretório original
        try:
            os.chdir(original_dir)
        except:
            pass

def main():
    """Função principal"""
    if len(sys.argv) < 3:
        print_colored("ERRO: Argumentos insuficientes", "red")
        print_colored("Uso: python test_conversion.py <caminho_rfa> <caminho_fbx>", "yellow")
        print_colored("Exemplo: python test_conversion.py \"C:\\Users\\<USER>\\Downloads\\Furniture_Chairs_Plank_Blocco-Chair.rfa\" \"C:\\temp\\output.fbx\"", "yellow")
        sys.exit(1)
    
    rfa_path = sys.argv[1].strip('"')
    fbx_path = sys.argv[2].strip('"')
    
    # Garantir extensão .fbx
    if not fbx_path.lower().endswith('.fbx'):
        fbx_path += '.fbx'
    
    success = run_pyrevit_conversion(rfa_path, fbx_path)
    
    if success:
        sys.exit(0)
    else:
        sys.exit(1)

if __name__ == "__main__":
    main()
