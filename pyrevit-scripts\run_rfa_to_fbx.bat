@echo off
REM Script batch para executar a conversão RFA para FBX usando PyRevit headless
REM 
REM Uso: run_rfa_to_fbx.bat "caminho_para_rfa" "caminho_para_fbx_saida"
REM 
REM Exemplo:
REM run_rfa_to_fbx.bat "C:\Users\<USER>\Downloads\Furniture_Chairs_Plank_Blocco-Chair.rfa" "C:\temp\output.fbx"

setlocal enabledelayedexpansion

REM Verificar se os argumentos foram fornecidos
if "%~1"=="" (
    echo ERRO: Caminho do arquivo RFA nao fornecido.
    echo Uso: %0 "caminho_para_rfa" "caminho_para_fbx_saida"
    echo Exemplo: %0 "C:\Users\<USER>\Downloads\Furniture_Chairs_Plank_Blocco-Chair.rfa" "C:\temp\output.fbx"
    exit /b 1
)

if "%~2"=="" (
    echo ERRO: Caminho de saida FBX nao fornecido.
    echo Uso: %0 "caminho_para_rfa" "caminho_para_fbx_saida"
    echo Exemplo: %0 "C:\Users\<USER>\Downloads\Furniture_Chairs_Plank_Blocco-Chair.rfa" "C:\temp\output.fbx"
    exit /b 1
)

REM Definir caminhos
set "RFA_PATH=%~1"
set "FBX_PATH=%~2"
set "PYREVIT_PATH=C:\Users\<USER>\AppData\Roaming\pyRevit-Master"
set "PYREVIT_PYTHON=%PYREVIT_PATH%\bin\cengines\CPY3123\python.exe"
set "REVIT_PATH=C:\Program Files\Autodesk\Revit 2024\Revit.exe"
set "SCRIPT_PATH=%~dp0export_rfa_to_fbx.py"

echo ========================================
echo CONVERSAO RFA PARA FBX - PyRevit Headless
echo ========================================
echo RFA Input: %RFA_PATH%
echo FBX Output: %FBX_PATH%
echo PyRevit Path: %PYREVIT_PATH%
echo Script Path: %SCRIPT_PATH%
echo ========================================

REM Verificar se os arquivos/diretórios existem
if not exist "%RFA_PATH%" (
    echo ERRO: Arquivo RFA nao encontrado: %RFA_PATH%
    exit /b 1
)

if not exist "%PYREVIT_PATH%" (
    echo ERRO: PyRevit nao encontrado em: %PYREVIT_PATH%
    exit /b 1
)

if not exist "%PYREVIT_PYTHON%" (
    echo ERRO: Python do PyRevit nao encontrado em: %PYREVIT_PYTHON%
    exit /b 1
)

if not exist "%REVIT_PATH%" (
    echo ERRO: Revit nao encontrado em: %REVIT_PATH%
    exit /b 1
)

if not exist "%SCRIPT_PATH%" (
    echo ERRO: Script PyRevit nao encontrado em: %SCRIPT_PATH%
    exit /b 1
)

REM Criar diretório de saída se não existir
for %%F in ("%FBX_PATH%") do set "FBX_DIR=%%~dpF"
if not exist "%FBX_DIR%" (
    echo INFO: Criando diretorio de saida: %FBX_DIR%
    mkdir "%FBX_DIR%"
)

echo INFO: Iniciando conversao RFA para FBX...
echo INFO: Executando PyRevit headless...

REM Executar o script PyRevit usando pyrevit run
REM Nota: O pyrevit run deve estar no PATH ou usar o caminho completo
cd /d "%PYREVIT_PATH%"

REM Tentar executar usando pyrevit CLI
echo INFO: Tentando executar com pyrevit CLI...
pyrevit run "%SCRIPT_PATH%" -- "%RFA_PATH%" "%FBX_PATH%"

if %ERRORLEVEL% EQU 0 (
    echo ========================================
    echo SUCESSO: Conversao RFA para FBX concluida!
    echo Arquivo FBX criado em: %FBX_PATH%
    echo ========================================
    exit /b 0
) else (
    echo ========================================
    echo ERRO: Falha na conversao RFA para FBX
    echo Codigo de erro: %ERRORLEVEL%
    echo ========================================
    exit /b %ERRORLEVEL%
)
