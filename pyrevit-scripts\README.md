# Scripts PyRevit para Conversão RFA para FBX

Este diretório contém scripts para converter arquivos de família do Revit (RFA) para formato FBX usando PyRevit em modo headless.

## Arquivos

### 1. `export_rfa_to_fbx.py`
Script principal PyRevit (IronPython2) que realiza a conversão:
- Cria um novo projeto Revit
- Carrega a família RFA
- Instancia a família na origem
- Exporta para FBX com máxima qualidade geométrica
- Fecha o projeto sem salvar

### 2. `run_rfa_to_fbx.bat`
Script batch para Windows que executa a conversão de forma automatizada.

### 3. `run_rfa_to_fbx.ps1`
Script PowerShell alternativo com melhor tratamento de erros e saída colorida.

### 4. `test_conversion.py`
Script Python para testar a conversão com validações e relatórios detalhados.

## Pré-requisitos

1. **Revit 2024** instalado em: `C:\Program Files\Autodesk\Revit 2024\Revit.exe`
2. **PyRevit** instalado em: `C:\Users\<USER>\AppData\Roaming\pyRevit-Master`
3. **PyRevit CLI** configurado no PATH do sistema

## Como Usar

### Método 1: Script Batch (Mais Simples)
```batch
run_rfa_to_fbx.bat "C:\Users\<USER>\Downloads\Furniture_Chairs_Plank_Blocco-Chair.rfa" "C:\temp\output.fbx"
```

### Método 2: Script PowerShell (Recomendado)
```powershell
.\run_rfa_to_fbx.ps1 -RfaPath "C:\Users\<USER>\Downloads\Furniture_Chairs_Plank_Blocco-Chair.rfa" -FbxPath "C:\temp\output.fbx"
```

### Método 3: PyRevit CLI Direto
```bash
pyrevit run export_rfa_to_fbx.py -- "C:\Users\<USER>\Downloads\Furniture_Chairs_Plank_Blocco-Chair.rfa" "C:\temp\output.fbx"
```

### Método 4: Script de Teste Python
```bash
python test_conversion.py "C:\Users\<USER>\Downloads\Furniture_Chairs_Plank_Blocco-Chair.rfa" "C:\temp\output.fbx"
```

## Configurações de Qualidade

O script está configurado para máxima preservação geométrica:
- `UseLevelsOfDetail = False` - Não usa LOD para manter todos os detalhes
- Sem decimação de mesh
- Preservação de curvas e superfícies
- Configurações de alta qualidade mesmo que resultem em arquivos maiores

## Categorias de Família Suportadas

O script tenta encontrar famílias nas seguintes categorias:
- Modelo Genérico (OST_GenericModel)
- Mobiliário (OST_Furniture)
- Sistemas de Mobiliário (OST_FurnitureSystems)
- Marcenaria (OST_Casework)
- Equipamentos Especiais (OST_SpecialityEquipment)
- Louças Sanitárias (OST_PlumbingFixtures)
- Equipamentos Elétricos (OST_ElectricalFixtures)
- Luminárias (OST_LightingFixtures)

## Tratamento de Erros

O script inclui tratamento robusto de erros para:
- Arquivos RFA não encontrados
- Falhas no carregamento de famílias
- Problemas na instanciação
- Erros na exportação FBX
- Limpeza automática de recursos

## Logs e Debugging

Todos os scripts fornecem logs detalhados:
- Mensagens de INFO para progresso normal
- Mensagens de AVISO para situações não críticas
- Mensagens de ERRO para falhas
- Stack traces completos para debugging

## Exemplo de Uso Completo

```bash
# Usando o script de teste com validações
python test_conversion.py "C:\Users\<USER>\Downloads\Furniture_Chairs_Plank_Blocco-Chair.rfa" "C:\temp\chair.fbx"

# Saída esperada:
# ==================================================
# CONVERSÃO RFA PARA FBX - PyRevit Headless
# ==================================================
# RFA Input: C:\Users\<USER>\Downloads\Furniture_Chairs_Plank_Blocco-Chair.rfa
# FBX Output: C:\temp\chair.fbx
# PyRevit Path: C:\Users\<USER>\AppData\Roaming\pyRevit-Master
# ==================================================
# INFO: Iniciando conversão RFA para FBX...
# INFO: Executando comando: pyrevit run export_rfa_to_fbx.py -- ...
# === INICIANDO CONVERSÃO RFA PARA FBX ===
# INFO: Criando um novo documento de projeto...
# INFO: Novo documento de projeto criado com sucesso.
# INFO: Carregando família de: C:\Users\<USER>\Downloads\Furniture_Chairs_Plank_Blocco-Chair.rfa
# INFO: Família carregada com sucesso.
# INFO: Procurando símbolos de família...
# INFO: Usando o símbolo de família: Blocco-Chair
# INFO: Instanciando a família na origem...
# INFO: Instância da família criada com sucesso.
# INFO: Exportando para FBX: C:\temp\chair.fbx
# INFO: Usando a vista para exportação: {3D}
# INFO: Exportado com sucesso para C:\temp\chair.fbx
# === CONVERSÃO CONCLUÍDA COM SUCESSO ===
# INFO: Fechando o documento do projeto sem salvar...
# INFO: Processo finalizado.
# ==================================================
# SUCESSO: Conversão RFA para FBX concluída!
# Arquivo FBX criado em: C:\temp\chair.fbx
# ==================================================
# INFO: Tamanho do arquivo FBX: 245760 bytes
```

## Solução de Problemas

### Erro: "pyrevit command not found"
- Verifique se o PyRevit CLI está instalado e no PATH
- Execute `pyrevit --version` para testar

### Erro: "Revit not found"
- Verifique se o Revit 2024 está instalado no caminho padrão
- Ajuste o caminho nos scripts se necessário

### Erro: "Family not found"
- Verifique se o arquivo RFA existe e não está corrompido
- Tente abrir o arquivo manualmente no Revit primeiro

### Erro: "Export failed"
- Verifique permissões de escrita no diretório de saída
- Certifique-se de que há espaço em disco suficiente

## Integração com BIMEX

Estes scripts são projetados para integração com o sistema BIMEX para conversão automática de famílias Revit para visualização 3D na web.
