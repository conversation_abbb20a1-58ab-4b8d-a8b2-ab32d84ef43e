# Script PowerShell para executar a conversão RFA para FBX usando PyRevit headless
#
# Uso: .\run_rfa_to_fbx.ps1 -RfaPath "caminho_para_rfa" -FbxPath "caminho_para_fbx_saida"
#
# Exemplo:
# .\run_rfa_to_fbx.ps1 -RfaPath "C:\Users\<USER>\Downloads\Furniture_Chairs_Plank_Blocco-Chair.rfa" -FbxPath "C:\temp\output.fbx"

param(
    [Parameter(Mandatory=$true)]
    [string]$RfaPath,
    
    [Parameter(Mandatory=$true)]
    [string]$FbxPath,
    
    [string]$PyRevitPath = "C:\Users\<USER>\AppData\Roaming\pyRevit-Master",
    [string]$RevitPath = "C:\Program Files\Autodesk\Revit 2024\Revit.exe"
)

# Função para escrever mensagens coloridas
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

# Função para verificar se um arquivo/diretório existe
function Test-PathExists {
    param(
        [string]$Path,
        [string]$Description
    )
    
    if (-not (Test-Path $Path)) {
        Write-ColorOutput "ERRO: $Description não encontrado em: $Path" "Red"
        return $false
    }
    return $true
}

# Cabeçalho
Write-ColorOutput "========================================" "Cyan"
Write-ColorOutput "CONVERSÃO RFA PARA FBX - PyRevit Headless" "Cyan"
Write-ColorOutput "========================================" "Cyan"
Write-ColorOutput "RFA Input: $RfaPath" "Yellow"
Write-ColorOutput "FBX Output: $FbxPath" "Yellow"
Write-ColorOutput "PyRevit Path: $PyRevitPath" "Yellow"
Write-ColorOutput "========================================" "Cyan"

# Definir caminhos
$PyRevitPython = Join-Path $PyRevitPath "bin\cengines\CPY3123\python.exe"
$ScriptPath = Join-Path $PSScriptRoot "export_rfa_to_fbx.py"

# Verificar se todos os caminhos existem
$pathChecks = @(
    @{ Path = $RfaPath; Description = "Arquivo RFA" },
    @{ Path = $PyRevitPath; Description = "PyRevit" },
    @{ Path = $PyRevitPython; Description = "Python do PyRevit" },
    @{ Path = $RevitPath; Description = "Revit" },
    @{ Path = $ScriptPath; Description = "Script PyRevit" }
)

foreach ($check in $pathChecks) {
    if (-not (Test-PathExists -Path $check.Path -Description $check.Description)) {
        exit 1
    }
}

# Criar diretório de saída se não existir
$FbxDir = Split-Path $FbxPath -Parent
if (-not (Test-Path $FbxDir)) {
    Write-ColorOutput "INFO: Criando diretório de saída: $FbxDir" "Green"
    New-Item -ItemType Directory -Path $FbxDir -Force | Out-Null
}

Write-ColorOutput "INFO: Iniciando conversão RFA para FBX..." "Green"

try {
    # Mudar para o diretório do PyRevit
    Push-Location $PyRevitPath
    
    # Tentar executar usando pyrevit CLI
    Write-ColorOutput "INFO: Executando PyRevit headless..." "Green"
    
    # Construir comando
    $pyrevitCmd = "pyrevit"
    $arguments = @("run", "`"$ScriptPath`"", "--", "`"$RfaPath`"", "`"$FbxPath`"")
    
    Write-ColorOutput "INFO: Comando: $pyrevitCmd $($arguments -join ' ')" "Gray"
    
    # Executar comando
    $process = Start-Process -FilePath $pyrevitCmd -ArgumentList $arguments -Wait -PassThru -NoNewWindow
    
    if ($process.ExitCode -eq 0) {
        Write-ColorOutput "========================================" "Green"
        Write-ColorOutput "SUCESSO: Conversão RFA para FBX concluída!" "Green"
        Write-ColorOutput "Arquivo FBX criado em: $FbxPath" "Green"
        Write-ColorOutput "========================================" "Green"
        exit 0
    } else {
        Write-ColorOutput "========================================" "Red"
        Write-ColorOutput "ERRO: Falha na conversão RFA para FBX" "Red"
        Write-ColorOutput "Código de erro: $($process.ExitCode)" "Red"
        Write-ColorOutput "========================================" "Red"
        exit $process.ExitCode
    }
    
} catch {
    Write-ColorOutput "ERRO: Exceção durante execução: $($_.Exception.Message)" "Red"
    exit 1
} finally {
    # Voltar ao diretório original
    Pop-Location
}
