# Script PowerShell para executar conversão RFA para FBX usando Revit + PyRevit
# Usando os caminhos corretos fornecidos

param(
    [string]$RevitPath = "C:\Program Files\Autodesk\Revit 2024\Revit.exe",
    [string]$PyRevitPath = "C:\Users\<USER>\AppData\Roaming\pyRevit-Master",
    [string]$RfaFile = "C:\Users\<USER>\OneDrive\old\Documentos\bimex-object-market\bimex-object-market\temp\cadeira.rfa",
    [string]$FbxFile = "C:\Users\<USER>\OneDrive\old\Documentos\bimex-object-market\bimex-object-market\temp\cadeira.fbx"
)

function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

Write-ColorOutput "========================================" "Cyan"
Write-ColorOutput "CONVERSÃO RFA PARA FBX - Revit + PyRevit" "Cyan"
Write-ColorOutput "========================================" "Cyan"
Write-ColorOutput "Revit: $RevitPath" "Yellow"
Write-ColorOutput "PyRevit: $PyRevitPath" "Yellow"
Write-ColorOutput "RFA: $RfaFile" "Yellow"
Write-ColorOutput "FBX: $FbxFile" "Yellow"
Write-ColorOutput "========================================" "Cyan"

# Verificar se os arquivos existem
$checks = @(
    @{ Path = $RevitPath; Description = "Revit" },
    @{ Path = $PyRevitPath; Description = "PyRevit" },
    @{ Path = $RfaFile; Description = "Arquivo RFA" }
)

foreach ($check in $checks) {
    if (-not (Test-Path $check.Path)) {
        Write-ColorOutput "ERRO: $($check.Description) não encontrado em: $($check.Path)" "Red"
        exit 1
    }
}

Write-ColorOutput "INFO: Todos os arquivos verificados com sucesso" "Green"

# Criar diretório de saída se não existir
$fbxDir = Split-Path $FbxFile -Parent
if (-not (Test-Path $fbxDir)) {
    Write-ColorOutput "INFO: Criando diretório de saída: $fbxDir" "Green"
    New-Item -ItemType Directory -Path $fbxDir -Force | Out-Null
}

# Tentar diferentes métodos de execução

Write-ColorOutput "INFO: Método 1 - Tentando pyrevit run..." "Blue"

try {
    # Mudar para o diretório do PyRevit
    Push-Location $PyRevitPath
    
    $scriptPath = Join-Path $PSScriptRoot "simple_rfa_to_fbx.py"
    
    # Tentar executar com pyrevit run
    $pyrevitCmd = "pyrevit"
    $arguments = @("run", "`"$scriptPath`"", "--revit=2024", "--allowdialogs")
    
    Write-ColorOutput "INFO: Executando: $pyrevitCmd $($arguments -join ' ')" "Gray"
    
    $process = Start-Process -FilePath $pyrevitCmd -ArgumentList $arguments -Wait -PassThru -NoNewWindow
    
    if ($process.ExitCode -eq 0) {
        Write-ColorOutput "INFO: PyRevit run executado com sucesso" "Green"
    } else {
        Write-ColorOutput "AVISO: PyRevit run falhou com código: $($process.ExitCode)" "Yellow"
    }
    
} catch {
    Write-ColorOutput "AVISO: Erro ao executar pyrevit run: $($_.Exception.Message)" "Yellow"
} finally {
    Pop-Location
}

# Verificar se o arquivo FBX foi criado
if (Test-Path $FbxFile) {
    $fileInfo = Get-Item $FbxFile
    Write-ColorOutput "========================================" "Green"
    Write-ColorOutput "SUCESSO: Arquivo FBX criado!" "Green"
    Write-ColorOutput "Local: $FbxFile" "Green"
    Write-ColorOutput "Tamanho: $($fileInfo.Length) bytes" "Green"
    Write-ColorOutput "Criado em: $($fileInfo.CreationTime)" "Green"
    Write-ColorOutput "========================================" "Green"
    exit 0
} else {
    Write-ColorOutput "INFO: Método 2 - Tentando execução direta do Revit..." "Blue"
    
    try {
        # Criar um script de journal para automatizar o Revit
        $journalFile = Join-Path $env:TEMP "rfa_to_fbx_journal.txt"
        $scriptPath = Join-Path $PSScriptRoot "simple_rfa_to_fbx.py"
        
        $journalContent = @"
'Creating new project
Jrn.Command "Ribbon" , "Create a new project , ID_REVIT_FILE_NEW_PROJECT"
Jrn.Data "TaskDialogResult" , "This action will close the active project. Do you want to save the changes to Untitled?" , "No", "IDNO"
'Run PyRevit script
Jrn.Command "Ribbon" , "Run a pyRevit command , ID_PYREVIT_COMMAND"
Jrn.Data "PyRevitCommand" , "$scriptPath"
'Exit Revit
Jrn.Command "SystemMenu" , "Quit the application; prompts to save projects , ID_APP_EXIT"
"@
        
        Set-Content -Path $journalFile -Value $journalContent -Encoding UTF8
        
        Write-ColorOutput "INFO: Journal criado: $journalFile" "Gray"
        Write-ColorOutput "INFO: Executando Revit com journal..." "Blue"
        
        # Executar Revit com journal
        $revitProcess = Start-Process -FilePath $RevitPath -ArgumentList "`"$journalFile`"" -Wait -PassThru
        
        Write-ColorOutput "INFO: Revit finalizado com código: $($revitProcess.ExitCode)" "Blue"
        
        # Verificar novamente se o arquivo foi criado
        if (Test-Path $FbxFile) {
            $fileInfo = Get-Item $FbxFile
            Write-ColorOutput "========================================" "Green"
            Write-ColorOutput "SUCESSO: Arquivo FBX criado via journal!" "Green"
            Write-ColorOutput "Local: $FbxFile" "Green"
            Write-ColorOutput "Tamanho: $($fileInfo.Length) bytes" "Green"
            Write-ColorOutput "========================================" "Green"
            exit 0
        }
        
    } catch {
        Write-ColorOutput "ERRO: Falha ao executar Revit com journal: $($_.Exception.Message)" "Red"
    }
    
    Write-ColorOutput "========================================" "Red"
    Write-ColorOutput "ERRO: Arquivo FBX não foi criado" "Red"
    Write-ColorOutput "Verifique os logs do Revit para mais detalhes" "Red"
    Write-ColorOutput "========================================" "Red"
    exit 1
}
